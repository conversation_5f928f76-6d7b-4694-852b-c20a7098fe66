/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.interactive-session {
	max-width: 850px;
	margin: auto;
	position: relative; /* For chat dnd */
}

.interactive-list > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row > .monaco-tl-row > .monaco-tl-twistie {
	/* Hide twisties from chat tree rows, but not from nested trees within a chat response */
	display: none !important;
}

.interactive-item-container {
	padding: 12px 16px;
	display: flex;
	flex-direction: column;
	color: var(--vscode-interactive-session-foreground);

	cursor: default;
	user-select: text;
	-webkit-user-select: text;
}

.interactive-item-container:not(:has(.chat-extensions-content-part)) .header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	position: relative;
	margin-bottom: 8px;
}

.interactive-item-container .header.hidden,
.interactive-item-container .header .avatar-container.hidden,
.interactive-item-container .header .username.hidden {
	display: none;
}

.interactive-item-container .header .user {
	display: flex;
	align-items: center;
	gap: 8px;

	/*
	Rendering the avatar icon as round makes it a little larger than the .user container.
	Add padding so that the focus outline doesn't run into it, and counteract it with a negative margin so it doesn't actually take up any extra space */
	padding: 2px;
	margin: -2px;
}

.interactive-item-container .header .username {
	margin: 0;
	font-size: 13px;
	font-weight: 600;
}

.interactive-item-container .detail-container {
	font-size: 12px;
	color: var(--vscode-descriptionForeground);
	overflow: hidden;
}

.interactive-item-container .detail-container .detail .agentOrSlashCommandDetected A {
	cursor: pointer;
	color: var(--vscode-textLink-foreground);
}

.interactive-item-container .chat-animated-ellipsis {
	display: inline-block;
	width: 11px;
}

.interactive-item-container:not(.show-detail-progress) .chat-animated-ellipsis {
	display: none;
}

@keyframes ellipsis {
	0% {
		content: "";
	}
	25% {
		content: ".";
	}
	50% {
		content: "..";
	}
	75% {
		content: "...";
	}
	100% {
		content: "";
	}
}

.interactive-item-container .chat-animated-ellipsis::after {
	content: '';
	white-space: nowrap;
	overflow: hidden;
	width: 3em;
	animation: ellipsis steps(4, end) 1s infinite;
}

.interactive-item-container .header .avatar-container {
	display: flex;
	pointer-events: none;
	user-select: none;
}

.interactive-item-container .header .avatar {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	outline: 1px solid var(--vscode-chat-requestBorder);
}

.interactive-item-container .header .avatar.codicon-avatar {
	background: var(--vscode-chat-avatarBackground);
}

.interactive-item-container .header .avatar+.avatar {
	margin-left: -8px;
}

.interactive-item-container .header .avatar .icon {
	width: 24px;
	height: 24px;
	border-radius: 50%;
	background-color: var(--vscode-chat-list-background);
}

.interactive-item-container .header .avatar .codicon {
	color: var(--vscode-chat-avatarForeground) !important;
	font-size: 14px;
}

.monaco-list-row:not(.focused) .interactive-item-container:not(:hover) .header .monaco-toolbar,
.monaco-list:not(:focus-within) .monaco-list-row .interactive-item-container:not(:hover) .header .monaco-toolbar,
.monaco-list-row:not(.focused) .interactive-item-container:not(:hover) .header .monaco-toolbar .action-label,
.monaco-list:not(:focus-within) .monaco-list-row .interactive-item-container:not(:hover) .header .monaco-toolbar .action-label {
	/* Also apply this rule to the .action-label directly to work around a strange issue- when the
	toolbar is hidden without that second rule, tabbing from the list container into a list item doesn't work
	and the tab key doesn't do anything. */
	display: none;
}

.interactive-item-container .header .monaco-toolbar .monaco-action-bar .actions-container {
	gap: 4px;
}

.interactive-item-container .header .monaco-toolbar .action-label {
	border: 1px solid transparent;
	padding: 2px;
}

.interactive-item-container.interactive-response .header .monaco-toolbar {
	position: absolute;
	left: 0px;
	background-color: var(--vscode-chat-list-background);
}

.interactive-item-container.interactive-request .header .monaco-toolbar {
	/* Take the partially-transparent background color override for request rows */
	background-color: inherit;
}

.interactive-item-container .chat-footer-toolbar {
	display: none;
}

.interactive-item-container .chat-footer-toolbar .monaco-action-bar .actions-container {
	gap: 4px;
}

.interactive-item-container .chat-footer-toolbar .checked.action-label,
.interactive-item-container .chat-footer-toolbar .checked.action-label:hover {
	color: var(--vscode-inputOption-activeForeground) !important;
	border-color: var(--vscode-inputOption-activeBorder);
	background-color: var(--vscode-inputOption-activeBackground);
}

.interactive-item-container.interactive-response.chat-most-recent-response {
	min-height: var(--chat-current-response-min-height);
}

.interactive-item-container.interactive-response:not(.chat-response-loading) .chat-footer-toolbar {
	/* Complete response only */
		display: block;
		opacity: 0;
		visibility: hidden;
		padding-top: 6px;
		height: 22px;
}

/* Show toolbar on hover and last response */
.interactive-item-container.interactive-response:not(.chat-response-loading):hover .chat-footer-toolbar,
.interactive-item-container.interactive-response.chat-most-recent-response:not(.chat-response-loading) .chat-footer-toolbar {
	opacity: 1;
	visibility: visible;
}

.interactive-item-container .value {
	width: 100%;
}

.interactive-item-container > .value .chat-used-context {
	margin-bottom: 8px;
}

.interactive-item-container .value .rendered-markdown:not(:has(.chat-extensions-content-part)) {
	.codicon {
		font-size: inherit;
	}

	.interactive-result-code-block .codicon {
		font-size: initial;
	}
}

.interactive-item-container .value .rendered-markdown blockquote {
	margin: 0px;
	padding: 0px 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
	border-radius: 2px;
	background: var(--vscode-textBlockQuote-background);
	border-color: var(--vscode-textBlockQuote-border);
}

.interactive-item-container .value .rendered-markdown table {
	width: 100%;
	text-align: left;
	margin-bottom: 16px;
}

.interactive-item-container .value .rendered-markdown table,
.interactive-item-container .value .rendered-markdown table td,
.interactive-item-container .value .rendered-markdown table th {
	border: 1px solid var(--vscode-chat-requestBorder);
	border-collapse: collapse;
	padding: 4px 6px;
}

.interactive-item-container .value .rendered-markdown a,
.interactive-item-container .value .interactive-session-followups,
.interactive-item-container .value .rendered-markdown a code {
	color: var(--vscode-textLink-foreground);
}

.interactive-item-container .value .rendered-markdown .chat-extensions-content-part a {
	color: inherit;
}

.interactive-item-container .value .rendered-markdown a {
	user-select: text;
}

.interactive-item-container .value .rendered-markdown a:hover,
.interactive-item-container .value .rendered-markdown a:active {
	color: var(--vscode-textLink-activeForeground);
}

.hc-black .interactive-item-container .value .rendered-markdown a code,
.hc-light .interactive-item-container .value .rendered-markdown a code {
	color: var(--vscode-textPreformat-foreground);
}

.interactive-list {
	overflow: hidden;
	position: relative; /* For the scroll down button */
}

.hc-black .interactive-request,
.hc-light .interactive-request {
	border-left: 3px solid var(--vscode-chat-requestBorder);
	border-right: 3px solid var(--vscode-chat-requestBorder);
}

.interactive-item-container .value {
	white-space: normal;
	overflow-wrap: anywhere;
}

.interactive-item-container .value > :last-child,
.interactive-item-container .value > :last-child.rendered-markdown > :last-child,
.interactive-item-container.interactive-request .value .rendered-markdown > :last-child {
	margin-bottom: 0px;
}

.interactive-item-container .value .rendered-markdown hr {
	border-color: rgba(0, 0, 0, 0.18);
}

.vs-dark .interactive-item-container .value .rendered-markdown hr {
	border-color: rgba(255, 255, 255, 0.18);
}

.interactive-item-container .value .rendered-markdown h1 {
	font-size: 20px;
	font-weight: 600;
	margin: 16px 0 8px 0;

}

.interactive-item-container .value .rendered-markdown h2 {
	font-size: 16px;
	font-weight: 600;
	margin: 16px 0 8px 0;
}

.interactive-item-container .value .rendered-markdown h3 {
	font-size: 14px;
	font-weight: 600;
	margin: 16px 0 8px 0;
}

.interactive-item-container.editing-session .value .rendered-markdown p:has(+ [data-code] > .chat-codeblock-pill-widget) {
	margin-bottom: 8px;
}

.interactive-item-container.editing-session .value .rendered-markdown h3 {
	font-size: 13px;
	margin: 0 0 8px 0;
	font-weight: unset;
}

/* Codicons next to text need to be aligned with the text */
.interactive-item-container .value .rendered-markdown:not(:has(.chat-extensions-content-part)) .codicon {
	position: relative;
	top: 2px;
}

.interactive-item-container .value .rendered-markdown {
	.chat-codeblock-pill-widget .codicon {
		top: -1px;
	}

	/* But codicons in toolbars assume the natural position of the codicon */
	.monaco-toolbar .codicon {
		position: initial;
		top: initial;
	}

	/* Code blocks at the beginning of an answer should not have a margin as it means it won't align with the agent icon*/
	> div[data-code]:first-child {
		margin-top: 0;

	}
	/* Override the top to avoid the toolbar getting clipped by overflow:hidden */
	> div[data-code]:first-child .interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-action-bar,
	> div[data-code]:first-child .interactive-result-code-block .interactive-result-code-block-toolbar > .monaco-toolbar {
		top: 6px;
	}
}

.interactive-item-container .value.inline-progress {

	.rendered-markdown {
		display: inline-flex;
	}

	/* not ideal but I cannot query the last div with this class... */
	.rendered-markdown:last-of-type>P>SPAN:empty {
		display: inline-block;
		width: 11px;
	}

	.rendered-markdown:last-of-type>P>SPAN:empty::after {
		content: '';
		white-space: nowrap;
		overflow: hidden;
		width: 3em;
		animation: ellipsis steps(4, end) 1s infinite;
	}
}

.interactive-item-container .value .rendered-markdown {
	line-height: 1.5em;
}

.interactive-item-container .value > .rendered-markdown p {
	/* Targetting normal text paras. `p` can also appear in other elements/widgets */
	margin: 0 0 16px 0;
}

.interactive-item-container .value > .chat-tool-invocation-part {
	.rendered-markdown p {
		margin: 0 0 6px 0;
		opacity: 0.85;
	}

	.disclaimer {
		margin-top: 6px;
		margin-bottom: -6px;

		.rendered-markdown p:last-child {
			margin-bottom: 0;
		}
	}

	.message .see-more {
		display: none;
		color: var(--vscode-textLink-foreground);
		text-decoration: underline;
		cursor: pointer;
	}

	.message.can-see-more {
		.message-wrapper {
			mask-image: linear-gradient(#000 0%, transparent 100%);
			pointer-events: none;
			max-height: 2.5em;
		}

		.see-more {
			display: block;
		}
	}

	.progress-container .rendered-markdown [data-code] {
		margin: 0;
	}

	.tool-input-output-part {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
	}

	.tool-input-output-part .rendered-markdown p {
		margin: inherit;
	}

	.tool-input-output-part .expando {
		display: flex;
		align-items: center;
		cursor: pointer;
	}

	.tool-input-output-part .input-output {
		display: none;
		padding: 6px 0;
		flex-basis: 100%;
		width: 100%;
	}

	.tool-input-output-part.expanded .input-output {
		display: inherit;
	}

	&:not(:last-child) {
		margin-bottom: 8px;
	}
}

.interactive-item-container .value > .rendered-markdown li > p {
	margin: 0;
}

/* #region list indent rules */
.interactive-item-container .value .rendered-markdown ul {
	/* Keep this in sync with the values for dedented codeblocks below */
	padding-inline-start: 24px;
}

.interactive-item-container .value .rendered-markdown ol {
	/* Keep this in sync with the values for dedented codeblocks below */
	padding-inline-start: 28px;
}

/* NOTE- We want to dedent codeblocks in lists specifically to give them the full width. No more elegant way to do this, these values
have to be updated for changes to the rules above, or to support more deeply nested lists. */
.interactive-item-container .value .rendered-markdown ul .interactive-result-code-block {
	margin-left: -24px;
}

.interactive-item-container .value .rendered-markdown ul ul .interactive-result-code-block {
	margin-left: -48px;
}

.interactive-item-container .value .rendered-markdown ol .interactive-result-code-block {
	margin-left: -28px;
}

.interactive-item-container .value .rendered-markdown ol ol .interactive-result-code-block {
	margin-left: -56px;
}

.interactive-item-container .value .rendered-markdown ol ul .interactive-result-code-block,
.interactive-item-container .value .rendered-markdown ul ol .interactive-result-code-block {
	margin-left: -52px;
}

/* #endregion list indent rules */

.interactive-item-container .value .rendered-markdown img {
	max-width: 100%;
}

.chat-tool-hover, .interactive-item-container {
	.monaco-tokenized-source, code {
		font-family: var(--monaco-monospace-font);
		font-size: 12px;
		color: var(--vscode-textPreformat-foreground);
		background-color: var(--vscode-textPreformat-background);
		padding: 1px 3px;
		border-radius: 4px;
		white-space: pre-wrap;
	}
}

.interactive-item-container.interactive-item-compact {
	padding: 8px 20px;
}

.interactive-item-container.interactive-item-compact .header {
	height: 16px;
}

.interactive-item-container.interactive-item-compact .header .avatar {
	width: 18px;
	height: 18px;
}

.interactive-item-container.interactive-item-compact .header .avatar .icon {
	width: 16px;
	height: 16px;
}

.interactive-item-container.interactive-item-compact .header .codicon-avatar .codicon {
	font-size: 12px;
}

.interactive-item-container.interactive-item-compact .header .avatar+.avatar {
	margin-left: -4px;
}

.interactive-item-container.interactive-item-compact .value {
	min-height: 0;
}

.interactive-item-container.interactive-item-compact .value > .rendered-markdown p {
	margin: 0 0 8px 0;
}

.interactive-item-container.interactive-item-compact .value > .rendered-markdown li > p {
	margin: 0;
}

.interactive-item-container.interactive-item-compact .value .rendered-markdown h1 {
	margin: 8px 0;

}

.interactive-item-container.interactive-item-compact .value .rendered-markdown h2 {
	margin: 8px 0;
}

.interactive-item-container.interactive-item-compact .value .rendered-markdown h3 {
	margin: 8px 0;
}

.interactive-item-container.minimal {
	flex-direction: row;
}

.interactive-item-container.minimal .column.left {
	padding-top: 2px;
	display: inline-block;
	flex-grow: 0;
}

.interactive-item-container.minimal .column.right {
	display: inline-block;
	flex-grow: 1;
}

.interactive-item-container.interactive-request.minimal .rendered-markdown .chat-animated-ellipsis {
	display: inline-flex;
}

.interactive-item-container.minimal .user > .username {
	display: none;
}

.interactive-item-container.minimal .detail-container {
	font-size: unset;
}

.interactive-item-container.minimal > .header {
	position: absolute;
	right: 0;
}

.chat-dnd-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	box-sizing: border-box;

	display: none;
}

.chat-dnd-overlay.visible {
	display: flex;
	align-items: center;
	justify-content: center;
}

.chat-dnd-overlay .attach-context-overlay-text {
	padding: 0.6em;
	margin: 0.2em;
	line-height: 12px;
	height: 12px;
	display: flex;
	align-items: center;
	text-align: center;
}

.chat-dnd-overlay .attach-context-overlay-text .codicon {
	height: 12px;
	font-size: 12px;
	margin-right: 3px;
}

.interactive-session .chat-input-container {
	box-sizing: border-box;
	cursor: text;
	background-color: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border, transparent);
	border-radius: 4px;
	padding: 0 6px 6px 6px; /* top padding is inside the editor widget */
	max-width: 100%;
}

.interactive-session .chat-editing-session {
	margin-bottom: -4px;
	width: 100%;
	position: relative;
}

.interactive-session .chat-editing-session .chat-editing-session-container {
	margin-bottom: -13px;
	padding: 6px 8px 18px 8px;
	box-sizing: border-box;
	background-color: var(--vscode-editor-background);
	border: 1px solid var(--vscode-input-border, transparent);
	border-bottom: none;
	border-radius: 4px;
	display: flex;
	flex-direction: column;
	gap: 2px;
	overflow: hidden;
}

.interactive-session .chat-editing-session .monaco-list-row .chat-collapsible-list-action-bar {
	display: none;
	padding-right: 12px;
}

.interactive-session .chat-editing-session .monaco-list-row:hover .chat-collapsible-list-action-bar,
.interactive-session .chat-editing-session .monaco-list-row.focused .chat-collapsible-list-action-bar,
.interactive-session .chat-editing-session .monaco-list-row.selected .chat-collapsible-list-action-bar {
	display: inherit;
}

.interactive-session .chat-editing-session .chat-editing-session-container.show-file-icons .monaco-scrollable-element .monaco-list-rows .monaco-list-row {
	border-radius: 2px;
}

.interactive-session .chat-editing-session .chat-editing-session-container .monaco-list .monaco-list-row .monaco-icon-name-container.modified {
	font-weight: bold;
}

.interactive-session .chat-editing-session .chat-editing-session-container .chat-editing-session-overview {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	gap: 6px;
	padding: 0 4px;
}

.interactive-session .chat-editing-session .chat-editing-session-container .chat-editing-session-overview > .working-set-title {
	color: var(--vscode-descriptionForeground);
	font-size: 11px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	align-content: center;
}

.interactive-session .chat-editing-session .chat-editing-session-container .chat-editing-session-overview > .working-set-title .working-set-count.file-limit-reached {
	color: var(--vscode-notificationsWarningIcon-foreground);
}

.interactive-session .chat-editing-session .chat-editing-session-container .monaco-progress-container {
	position: relative;
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions,
.interactive-session .chat-editing-session .chat-editing-session-actions {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	gap: 6px;
	align-items: center;
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions {
	margin: 3px 0px;
	overflow: hidden;
}

.interactive-session .chat-editing-session .monaco-button {
	height: 17px;
	width: fit-content;
	padding: 2px 6px;
	font-size: 11px;
	background-color: var(--vscode-button-background);
	border: 1px solid var(--vscode-button-border);
	color: var(--vscode-button-foreground);
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button:hover {
	background-color: var(--vscode-button-hoverBackground);
}

.interactive-session .chat-editing-session .chat-editing-session-actions-group {
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	gap: 6px;
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button.codicon.codicon-close {
	width: 17px;
	height: 17px;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 16px;
	color: var(--vscode-descriptionForeground);
	background-color: transparent;
	border: none;
	padding: 0;
	border-radius: 5px;
	cursor: pointer;
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button.secondary {
	color: var(--vscode-foreground);
	background-color: transparent;
	border: none;
	height: 22px;
	padding-left: 0px;
	cursor: pointer;
	display: flex;
	justify-content: start;
}

.chat-attachments-container {
	display: flex;
	flex-direction: row;
	gap: 4px;
	margin-top: 6px;
	flex-wrap: wrap;
	cursor: default;
}

.chat-related-files {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	gap: 4px;
	max-width: 100%;
}

.chat-related-files .monaco-button-dropdown .monaco-text-button {
	font-size: 11px;
	justify-content: left;
	width: fit-content;
	padding: 0px;
	border: none;
	height: 18px;
}

.chat-related-files .monaco-button-dropdown .monaco-text-button span {
	font-style: italic;
	height: 18px;
	opacity: 0.7;
}

.chat-related-files .monaco-button-dropdown {
	border-radius: 4px;
	height: 18px;
	border: 1px solid var(--vscode-input-border);
	border-style: dashed;
	align-items: center;
	overflow: hidden;
	gap: 2px;
	padding: 0 4px;
}

.chat-related-files .monaco-button.codicon.codicon-add {
	display: flex;
	flex-direction: column;
	color: var(--vscode-descriptionForeground);
	padding-top: 3px;
	margin-left: -4px;
	padding-left: 4px;
	font-size: 14px; /* The + codicon is large, make it look more like the x codicon */
	height: calc(100% - 3px);
	width: 17px;
	outline-offset: -2px !important;
}

.interactive-session .chat-related-files .monaco-icon-label::before {
	padding: 4px 3px 0 2px;
}

.interactive-session .chat-editing-session .chat-related-files .monaco-button.secondary:first-child {
	margin: 3px 0px 3px 3px;
	flex-shrink: 0;
}

.interactive-session .chat-editing-session .chat-related-files .monaco-button.secondary.monaco-icon-label::before {
	display: inline-flex;
	align-items: center;
}

.interactive-session .chat-editing-session .chat-related-files .monaco-button.secondary:only-child {
	width: 100%;
}

.interactive-session .chat-editing-session .chat-related-files .monaco-button.secondary.disabled {
	cursor: initial;
}

.interactive-session .chat-editing-session .chat-related-files .monaco-button.secondary .codicon {
	font-size: 12px;
	margin-left: 4px;
}

.interactive-session .chat-editing-session .chat-editing-session-actions .monaco-button.secondary.monaco-text-button.codicon {
	background-color: transparent;
	border-color: transparent;
	color: var(--vscode-foreground);
	cursor: pointer;
	height: 16px;
	padding: 0px;
	border-radius: 2px;
	display: inline-flex;
}

.interactive-session .chat-editing-session .chat-editing-session-actions .monaco-button.secondary.monaco-text-button {
	background-color: var(--vscode-button-secondaryBackground);
	border: 1px solid var(--vscode-button-border);
	color: var(--vscode-button-secondaryForeground);
}

.interactive-session .chat-editing-session .chat-editing-session-actions .monaco-button.secondary:hover {
	background-color: var(--vscode-button-secondaryHoverBackground);
	color: var(--vscode-button-secondaryForeground);
}

/* The Add Files button is currently implemented as a secondary button but should not have the secondary button background */
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button.secondary:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
}

.interactive-session .chat-editing-session .chat-editing-session-actions .monaco-button.secondary.monaco-text-button.codicon:not(.disabled):hover,
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button,
.interactive-session .chat-editing-session .chat-editing-session-actions .monaco-button {
	overflow: hidden;
	text-wrap: nowrap;
}

.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button {
	align-items: center;
	border-radius: 2px;
}
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button .monaco-button,
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button .monaco-button:hover {
	border-right: 1px solid transparent;
	background-color: unset;
	padding: 0;
}
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button > .separator {
	border-right: 1px solid transparent;
	padding: 0 1px;
	height: 22px;
}
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button:hover > .separator {
	border-color: var(--vscode-input-border, transparent);
}
.interactive-session .chat-editing-session .chat-editing-session-toolbar-actions .monaco-button-dropdown.sidebyside-button:hover {
	background-color: var(--vscode-toolbar-hoverBackground);
}

.interactive-session .interactive-input-part.compact .chat-input-container {
	display: flex;
	justify-content: space-between;
	padding-bottom: 0;
	border-radius: 2px;
}

.interactive-session .interactive-input-and-side-toolbar {
	display: flex;
	gap: 4px;
	align-items: center;
	position: relative;
}

.interactive-session .chat-input-container.focused {
	border-color: var(--vscode-focusBorder);
}

.chat-editor-container .monaco-editor .mtk1 {
	color: var(--vscode-input-foreground);
}

.interactive-session .chat-editor-container .monaco-editor .chat-prompt-spinner {
	transform-origin: 6px 6px;
	font-size: 12px;
}

.interactive-session .interactive-input-part .chat-editor-container .interactive-input-editor .monaco-editor,
.interactive-session .interactive-input-part .chat-editor-container .interactive-input-editor .monaco-editor .monaco-editor-background {
	background-color: var(--vscode-input-background);
}

.interactive-session .interactive-input-part.editing .chat-input-container .chat-editor-container .monaco-editor,
.interactive-session .interactive-input-part.editing .chat-input-container .chat-editor-container .monaco-editor .monaco-editor-background,
.interactive-session .interactive-request.editing .interactive-input-part .chat-input-container .chat-editor-container .monaco-editor,
.interactive-session .interactive-request.editing .interactive-input-part .chat-input-container .chat-editor-container .monaco-editor .monaco-editor-background {
	background-color: transparent;
}

.interactive-session .interactive-input-part.editing .chat-input-container,
.interactive-session .interactive-request.editing .interactive-input-part .chat-input-container {
	background-color: var(--vscode-chat-requestBubbleBackground);
}


.interactive-session .chat-editor-container .monaco-editor .cursors-layer {
	padding-left: 4px;
}

.interactive-session .chat-input-toolbars {
	display: flex;
}

.interactive-session .chat-input-toolbars :first-child {
	margin-right: auto;
}

.interactive-session .chat-input-toolbars > .chat-input-toolbar {
	min-width: 0px;

	.chat-modelPicker-item {
		min-width: 0px;

		.action-label {
			min-width: 0px;

			.chat-model-label {
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}

		.codicon {
			flex-shrink: 0;
		}
	}
}

.chat-execute-toolbar .codicon.codicon-debug-pause {
	color: var(--vscode-icon-foreground) !important;
}

.interactive-session .chat-input-toolbars .chat-modelPicker-item .action-label {
	height: 16px;
	padding: 3px 0px 3px 6px;
	display: flex;
	align-items: center;
}


.interactive-session .chat-input-toolbars .chat-modelPicker-item .action-label .codicon-chevron-down {
	font-size: 12px;
	margin-left: 2px;
}

.interactive-session .chat-input-toolbars .monaco-action-bar .actions-container {
	display: flex;
	gap: 4px;
	overflow: hidden;
}

.interactive-session .chat-input-toolbars .codicon-debug-stop {
	color: var(--vscode-icon-foreground) !important;
}

.interactive-response .interactive-result-code-block .interactive-result-editor .monaco-editor,
.interactive-response .interactive-result-code-block .interactive-result-editor .monaco-editor .margin,
.interactive-response .interactive-result-code-block .interactive-result-editor .monaco-editor .monaco-editor-background {
	background-color: var(--vscode-interactive-result-editor-background-color) !important;
}

.interactive-item-compact .interactive-result-code-block {
	margin: 0 0 8px 0;
}

.interactive-item-container .interactive-result-code-block .monaco-toolbar .monaco-action-bar .actions-container {
	padding-inline-start: unset;
}


@keyframes kf-chat-editing-atomic-edit {
	0% {
		opacity: 0.8;
	}
	100% {
		opacity: 0;
	}
}

.monaco-editor .chat-editing-atomic-edit {
	z-index: 1;
	opacity: 0.8;
	background-color: var(--vscode-editor-background);
	animation: 350ms kf-chat-editing-atomic-edit ease-out;
}

.monaco-editor .chat-editing-pending-edit {
	z-index: 1;
	opacity: 0.6;
	background-color: var(--vscode-editor-background);
}

.monaco-editor .chat-editing-last-edit {
	background-color: var(--vscode-editor-rangeHighlightBackground);
	box-sizing: border-box;
	border: 1px solid var(--vscode-editor-rangeHighlightBorder);
}

@property --chat-editing-last-edit-shift {
	syntax: '<percentage>';
	initial-value: 100%;
	inherits: false;
}

@keyframes kf-chat-editing-last-edit-shift {
	0% {
		--chat-editing-last-edit-shift: 100%;
	}
	50% {
		--chat-editing-last-edit-shift: 7%;
	}
	100% {
		--chat-editing-last-edit-shift: 100%;
	}
}

.monaco-editor .chat-editing-last-edit-line {
	--chat-editing-last-edit-shift: 100%;
	background: linear-gradient(45deg, var(--vscode-editor-rangeHighlightBackground), var(--chat-editing-last-edit-shift), transparent);
	animation: 2.3s kf-chat-editing-last-edit-shift ease-in-out infinite;
	animation-delay: 330ms;
}


.chat-notification-widget .chat-info-codicon,
.chat-notification-widget .chat-error-codicon,
.chat-notification-widget .chat-warning-codicon {
	display: flex;
	align-items: start;
	gap: 8px;
}

.interactive-item-container .value .chat-notification-widget .rendered-markdown p {
	margin: 0;
}

.interactive-response .interactive-response-error-details {
	display: flex;
	align-items: start;
	gap: 6px;
}

.interactive-response .interactive-response-error-details .rendered-markdown :last-child {
	margin-bottom: 0px;
}

.chat-notification-widget .chat-info-codicon .codicon,
.chat-notification-widget .chat-error-codicon .codicon,
.chat-notification-widget .chat-warning-codicon .codicon {
	margin-top: 2px;
}

.interactive-response .interactive-response-error-details .codicon {
	margin-top: 1px;
}

.chat-used-context-list .codicon-warning {
	color: var(--vscode-notificationsWarningIcon-foreground); /* Have to override default styles which apply to all lists */
}

.chat-used-context-list .monaco-icon-label-container {
	color: var(--vscode-interactive-session-foreground);
}

.chat-attached-context .chat-attached-context-attachment .monaco-icon-name-container.warning,
.chat-attached-context .chat-attached-context-attachment .monaco-icon-suffix-container.warning,
.chat-used-context-list .monaco-icon-name-container.warning,
.chat-used-context-list .monaco-icon-suffix-container.warning {
	color: var(--vscode-notificationsWarningIcon-foreground);
}

.chat-attached-context .chat-attached-context-attachment.show-file-icons.warning,
.chat-attached-context .chat-attached-context-attachment.show-file-icons.partial-warning {
	border-color: var(--vscode-notificationsWarningIcon-foreground);
}

/**
 * Styles for the `reusable prompts` attachment widget.
 */
.chat-attached-context-attachment .prompt-type {
	opacity: 0.7;
	font-size: .9em;
	margin-left: 0.5em;
}
.chat-attached-context-attachment.warning {
	color: var(--vscode-notificationsWarningIcon-foreground);
}
.chat-attached-context-attachment.error {
	color: var(--vscode-notificationsErrorIcon-foreground);
}

.chat-attached-context-attachment .monaco-icon-label > .monaco-icon-label-container > .monaco-icon-suffix-container > .label-suffix {
	color: var(--vscode-peekViewTitleDescription-foreground);
	opacity: 1;
}

.chat-notification-widget .chat-warning-codicon .codicon-warning,
.chat-quota-error-widget .codicon-warning {
	color: var(--vscode-notificationsWarningIcon-foreground) !important; /* Have to override default styles which apply to all lists */
}

.chat-notification-widget .chat-error-codicon .codicon-error,
.interactive-response .interactive-response-error-details .codicon-error {
	color: var(--vscode-errorForeground) !important; /* Have to override default styles which apply to all lists */
}

.chat-notification-widget .chat-info-codicon .codicon-info,
.interactive-response .interactive-response-error-details .codicon-info {
	color: var(--vscode-notificationsInfoIcon-foreground) !important; /* Have to override default styles which apply to all lists */
}

.interactive-session .interactive-input-part {
	margin: 0px 16px;
	padding: 4px 0 12px 0px;
	display: flex;
	flex-direction: column;
	gap: 4px;
}

.interactive-session .interactive-input-part.compact {
	margin: 0;
	padding: 8px 0 0 0
}

.action-item.chat-attachment-button .action-label,
.interactive-session .chat-attached-context .chat-attached-context-attachment {
	display: flex;
	gap: 2px;
	overflow: hidden;
	font-size: 11px;
	padding: 0 4px;
	border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
	border-radius: 4px;
	height: 18px;
	max-width: 100%;
	width: fit-content;
}

.action-item.chat-attachment-button > .action-label > .codicon {
	font-size: 14px;
	height: auto;
}

.action-item.chat-mcp {
	display: flex !important;

	&.chat-mcp-has-action .action-label {
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
		border-right: 0;
	}

	.chat-mcp-action {
		align-self: stretch;
		padding: 0 2px;
		border-radius: 0;
		outline: 0;
		border: 0;
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
		background: var(--vscode-button-background);
		cursor: pointer;

		.codicon {
			width: fit-content;
			color: var(--vscode-button-foreground);
		}

		.codicon::before {
			font-size: 14px;
		}

		&.chat-mcp-action-error {
			background: var(--vscode-activityErrorBadge-background);

			.codicon {
				color: var(--vscode-activityErrorBadge-foreground);
			}
		}
	}
}

.action-item.chat-attached-context-attachment.chat-add-files .action-label.codicon::before {
	font: normal normal normal 16px/1 codicon;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-button {
	display: flex;
	align-items: center;
	margin-top: -2px;
	margin-right: -4px;
	padding-right: 4px;
	padding-left: 2px;
	height: calc(100% + 4px);
	outline-offset: -4px;
}

.chat-related-files .monaco-button.codicon.codicon-add:hover,
.action-item.chat-attached-context-attachment.chat-add-files:hover,
.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-button:hover {
	cursor: pointer;
	background: var(--vscode-toolbar-hoverBackground);
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label-container {
	display: flex;

	.monaco-icon-suffix-container {
		overflow: hidden;
		text-overflow: ellipsis;
	}
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label-container .monaco-highlighted-label {
	display: inline-flex;
	align-items: center;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label .monaco-button.codicon.codicon-close,
.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-button.codicon.codicon-close {
	color: var(--vscode-descriptionForeground);
	cursor: pointer;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label .codicon {
	font-size: 14px;
}

.interactive-session .chat-input-container .chat-attached-context {
	display: contents;
}

.interactive-session .chat-attached-context {
	display: flex;
	flex-wrap: wrap;
	gap: 4px;
}

.interactive-session .chat-attachment-toolbar .actions-container {
	gap: 4px;
	flex-wrap: wrap;
}

.interactive-session .interactive-input-part.compact .chat-attached-context {
	padding-bottom: 0px;
	display: flex;
	gap: 4px;
	flex-wrap: wrap;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit {
	display: flex;
	gap: 4px;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit .chat-implicit-hint {
	opacity: 0.7;
	font-size: .9em;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit.disabled .chat-implicit-hint {
	font-style: italic;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit.disabled {
	border-style: dashed;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit.disabled:focus {
	outline: none;
	border-color: var(--vscode-focusBorder);
}

.interactive-session .chat-attached-context .chat-attached-context-attachment.implicit.disabled .monaco-icon-label .label-name {
	text-decoration: line-through;
	font-style: italic;
	opacity: 0.8;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label {
	gap: 4px;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label::before {
	height: auto;
	padding: 0;
	line-height: 100% !important;
	align-self: center;

	background-size: contain;
	background-position: center;
	background-repeat: no-repeat;
}

.interactive-session .chat-attached-context .chat-attached-context-attachment .monaco-icon-label.predefined-file-icon::before {
	padding: 0 0 0 2px;
	align-content: center;
}

.interactive-session .interactive-item-container.interactive-request .chat-attached-context .chat-attached-context-attachment {
	padding-right: 6px;
}

.interactive-session-followups {
	display: flex;
	flex-direction: column;
	gap: 6px;
	align-items: start;
}

.interactive-session-followups .monaco-button {
	text-align: left;
	width: initial;
}

.interactive-session-followups .monaco-button .codicon {
	margin-left: 0;
	margin-top: 1px;
}

.interactive-item-container .interactive-response-followups .monaco-button {
	padding: 4px 8px;
}

/* .interactive-session .interactive-input-part .interactive-input-followups .interactive-session-followups {
	margin-bottom: 4px;
} */

.interactive-session .interactive-input-part .interactive-input-followups .interactive-session-followups .monaco-button {
	display: block;
	color: var(--vscode-textLink-foreground);
	font-size: 12px;

	/* clamp to max 3 lines */
	display: -webkit-box;
	line-clamp: 3;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.interactive-session .interactive-input-part .interactive-input-followups .interactive-session-followups code {
	font-family: var(--monaco-monospace-font);
	font-size: 11px;
}

.interactive-session .interactive-input-part .interactive-input-followups .interactive-session-followups .monaco-button .codicon-sparkle {
	float: left;
}

.interactive-session-followups .monaco-button.interactive-followup-reply {
	padding: 0px;
	border: none;
}

.interactive-item-container .monaco-toolbar .codicon {
	/* Very aggressive list styles try to apply focus colors to every codicon in a list row. */
	color: var(--vscode-icon-foreground) !important;
}

/* #region Quick Chat */

.quick-input-widget .interactive-session .interactive-input-part {
	padding: 8px 6px 8px 6px;
	margin: 0 3px;
}

.quick-input-widget .interactive-session .interactive-input-part .chat-input-toolbars .monaco-toolbar,
.quick-input-widget .interactive-session .interactive-input-part .chat-input-toolbars .actions-container {
	height: initial;
}

.quick-input-widget .interactive-session .interactive-input-part .chat-input-toolbars {
	margin-bottom: 1px;
	align-items: flex-end;
}

.quick-input-widget .interactive-session .chat-input-container {
	margin: 0;
	border-radius: 2px;
	padding: 0 4px 0 6px;
}

.quick-input-widget .interactive-list {
	border-bottom-right-radius: 6px;
	border-bottom-left-radius: 6px;
}

.quick-input-widget .interactive-response {
	min-height: 86px;
}

/* #endregion */

.interactive-response-progress-tree .monaco-list-row:not(.selected) .monaco-tl-row:hover {
	background-color: var(--vscode-list-hoverBackground);
}

.interactive-response-progress-tree {
	margin: 16px 0px;
}

.interactive-response-progress-tree.focused {
	border-color: var(--vscode-focusBorder, transparent);
}

.interactive-item-container .value .interactive-response-placeholder-codicon .codicon {
	color: var(--vscode-editorGhostText-foreground);
}

.interactive-item-container .value .interactive-response-placeholder-content {
	color: var(--vscode-editorGhostText-foreground);
	font-size: 12px;
	margin-bottom: 16px;
}

.interactive-item-container .value .interactive-response-placeholder-content p {
	margin: 0;
}

.interactive-response  .interactive-response-codicon-details {
	display: flex;
	align-items: start;
	gap: 6px;
}

.chat-used-context-list .monaco-list {
	border: none;
	border-radius: 4px;
	width: auto;
}

.interactive-item-container .chat-resource-widget {
	background-color: var(--vscode-chat-slashCommandBackground);
	color: var(--vscode-chat-slashCommandForeground);
}


.interactive-item-container .chat-resource-widget,
.interactive-item-container .chat-agent-widget .monaco-button {
	border-radius: 4px;
	padding: 1px 3px;
}

.interactive-item-container .chat-agent-command {
	background-color: var(--vscode-chat-slashCommandBackground);
	color: var(--vscode-chat-slashCommandForeground);
	display: inline-flex;
	align-items: center;
	margin-right: 0.5ch;
	border-radius: 4px;
	padding: 0 0 0 3px;
}

.interactive-item-container .chat-agent-command > .monaco-button {
	display: flex;
	align-self: stretch;
	align-items: center;
	cursor: pointer;
	padding: 0 2px;
	margin-left: 2px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.interactive-item-container .chat-agent-command > .monaco-button:hover {
	background: var(--vscode-toolbar-hoverBackground);
}

.interactive-item-container .chat-agent-widget .monaco-text-button {
	display: inline;
	border: none;
}

.interactive-session .chat-used-context.chat-used-context-collapsed .chat-used-context-list {
	display: none;
}

.interactive-session .chat-used-context {
	display: flex;
	flex-direction: column;
	gap: 2px;
}

.interactive-response-progress-tree,
.chat-notification-widget,
.chat-used-context-list,
.chat-quota-error-widget {
	border: 1px solid var(--vscode-chat-requestBorder);
	border-radius: 4px;
	margin-bottom: 8px;
}

.interactive-response-progress-tree,
.interactive-session .chat-used-context-list {
	padding: 4px 3px;

	.monaco-icon-label {
		padding: 0px 3px;
	}
}

.interactive-session .chat-editing-session-list {

	.monaco-icon-label {
		padding: 0px 3px;
	}

	.monaco-icon-label.excluded {
		color: var(--vscode-notificationsWarningIcon-foreground)
	}
}

.interactive-item-container .chat-notification-widget {
	padding: 8px 12px;
}

.interactive-session .chat-used-context-list .monaco-list .monaco-list-row {
	border-radius: 2px;
}

.interactive-session .chat-used-context-label {
	font-size: 12px;
	color: var(--vscode-descriptionForeground);
	user-select: none;

	code {
		font-size: 11px;
	}
}

.interactive-session .chat-used-context-label:hover {
	opacity: unset;
}

.interactive-session .chat-used-context-label .monaco-button {
	/* unset Button styles */
	display: inline-flex;
	width: fit-content;
	border: none;
	border-radius: 4px;
	gap: 4px;
	padding: 2px 6px 2px 2px;
	text-align: initial;
	justify-content: initial;
}

.interactive-session .chat-used-context-label .monaco-button:hover {
	background-color: var(--vscode-list-hoverBackground);
	color: var(--vscode-foreground);

}

.interactive-session .chat-used-context-label .monaco-text-button:focus {
	outline: none;
}

.interactive-session .chat-used-context-label .monaco-text-button:focus-visible {
	outline: 1px solid var(--vscode-focusBorder);
}

.interactive-session .chat-used-context-label .monaco-button .codicon {
	font-size: 12px;
}

.interactive-item-container .progress-container {
	display: flex;
	align-items: center;
	gap: 7px;
	margin: 0 0 6px 4px;

	/* Tool calls transition from a progress to a collapsible list part, which needs to have this top padding.
	The working progress also can be replaced by a tool progress part. So align this padding so the text doesn't appear to shift. */
	padding-top: 2px;

	> .codicon[class*='codicon-'] {
		height: 12px;
		font-size: 12px;

		&::before {
			font-size: 12px;
		}
	}

	.codicon {
		/* Very aggressive list styles try to apply focus colors to every codicon in a list row. */
		color: var(--vscode-icon-foreground) !important;

		&.codicon-check {
			color: var(--vscode-debugIcon-startForeground) !important;
		}
	}

	.rendered-markdown.progress-step {
		white-space: normal;

		& > p {
			color: var(--vscode-descriptionForeground);
			font-size: 12px;
			margin: 0;

			code {
				font-size: 11px;
			}
		}

		.chat-inline-anchor-widget {
			display: inline;
		}
	}
}

.interactive-item-container .chat-command-button {
	display: flex;
	margin-bottom: 16px;
}

.interactive-item-container .chat-notification-widget {
	display: flex;
	flex-direction: row;
	gap: 6px;
}

.interactive-item-container .chat-confirmation-widget .interactive-result-code-block,
.interactive-item-container .chat-confirmation-widget .chat-attached-context {
	margin-bottom: 8px;
}

.interactive-item-container .chat-command-button .monaco-button .codicon {
	margin-left: 0;
	margin-top: 1px;
}

.chat-code-citation-label {
	opacity: 0.7;
	white-space: pre-wrap;
}

.chat-code-citation-button-container {
	display: inline;
}

.chat-code-citation-button-container .monaco-button {
	display: inline;
	border: none;
	padding: 0;
	color: var(--vscode-textLink-foreground);
}

.chat-attached-context-hover {
	padding: 0 6px;
}

.chat-attached-context-hover .chat-attached-context-image-container {
	padding: 6px 0 4px;
	height: auto;
	width: 100%;
	display: block;
}

.chat-attached-context-hover .chat-attached-context-image-container .chat-attached-context-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
	display: block;
	max-height: 350px;
	max-width: 100%;
	min-width: 200px;
	min-height: 200px;

}

.chat-attached-context-hover .chat-attached-context-url {
	color: var(--vscode-textLink-foreground);
	cursor: pointer;
	margin-top: 4px;
	padding: 2px 0;
	width: 100%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
	display: block;
}

.chat-attached-context-hover .chat-attached-context-url-separator {
	border-top: 1px solid var(--vscode-chat-requestBorder);
	left: 0;
	right: 0;
	position: absolute;
	margin-top: 2px;
}

.chat-attached-context-attachment .chat-attached-context-pill {
	font-size: 12px;
	display: inline-flex;
	align-items: center;
	padding: 2px 0 2px 0px;
	border-radius: 2px;
	margin-right: 1px;
	user-select: none;
	outline: none;
	border: none;
}

.chat-attached-context-attachment .attachment-additional-info {
	opacity: 0.7;
	font-size: .9em;
}

.chat-attached-context-attachment .chat-attached-context-pill-image {
	width: 14px;
	height: 14px;
	border-radius: 2px;
	object-fit: cover;
}

.chat-attached-context-attachment .chat-attached-context-custom-text {
	vertical-align: middle;
	user-select: none;
	outline: none;
	border: none;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 100%;
	display: inline-block;
}

.chat-attached-context-attachment.show-file-icons.warning .chat-attached-context-custom-text {
	color: var(--vscode-notificationsWarningIcon-foreground);
	text-decoration: line-through;
}

.chat-attached-context-attachment.show-file-icons.partial-warning .chat-attached-context-custom-text {
	color: var(--vscode-notificationsWarningIcon-foreground);
}

.interactive-session .chat-scroll-down {
	display: none;
	position: absolute;
	bottom: 7px;
	right: 12px;
	border-radius: 100%;
	width: 27px;
	height: 27px;

	.codicon {
		margin: 0px;
	}
}

.interactive-session.show-scroll-down .chat-scroll-down {
	display: initial;
}

.chat-quota-error-widget {
	padding: 8px 12px;
	display: flex;
	gap: 6px;

	.monaco-button {
		width: fit-content;
		padding: 2px 11px;
	}

	.chat-quota-error-button {
		margin-top: 6px;
		margin-bottom: 2px;
	}

	.chat-quota-error-secondary-button {
		margin-top: 6px;
		padding: 0px;
		border: none;
	}

	.chat-quota-error-secondary-button,
	.chat-quota-wait-warning {
		font-size: 12px;
	}

	.chat-quota-wait-warning {
		margin-top: 2px;
	}

	.chat-quota-error-message {
		.rendered-markdown p {
			margin: 0px;
		}
	}
}

.hideSuggestTextIcons .suggest-widget .monaco-list .monaco-list-row .suggest-icon.codicon-symbol-text::before {
	display: none;
}

.interactive-session:not(.chat-widget > .interactive-session) {

	.interactive-item-container {
		padding: 5px 16px;
	}

	.interactive-item-container.interactive-request {
		align-items: flex-end;
		padding-bottom: 0px;
	}

	.interactive-item-container.interactive-request:not(.editing):hover .request-hover {
		opacity: 1 !important;
	}

	.interactive-item-container.interactive-request.confirmation-message {
		align-items: flex-start;
	}

	.interactive-item-container.interactive-request .value .rendered-markdown {
		background-color: var(--vscode-chat-requestBubbleBackground);
		border-radius: 8px;
		padding: 8px 12px;
		max-width: 90%;
		margin-left: auto;
		width: fit-content;
		margin-bottom: 5px;
		position: relative;
	}

	.interactive-item-container.interactive-request .value .rendered-markdown {
		margin-left: auto;
	}

	.interactive-item-container.interactive-request .value .rendered-markdown.clickable:hover {
		cursor: text;
		background-color: var(--vscode-chat-requestBubbleHoverBackground);
	}

	.hc-black .interactive-item-container.interactive-request .value .rendered-markdown,
	.hc-light .interactive-item-container.interactive-request .value .rendered-markdown {
		border: 1px dotted var(--vscode-focusBorder);
	}

	.interactive-item-container.interactive-request .value .rendered-markdown > :last-child {
		margin-bottom: 0px;
	}

	.interactive-item-container.interactive-request .value > .rendered-markdown p {
		width: fit-content;
	}

	.interactive-item-container.interactive-request .chat-attached-context {
		max-width: 100%;
		width: fit-content;
		justify-content: flex-end;
		margin-left: auto;
		padding-bottom: 5px;
	}

	.interactive-request .header.header-disabled,
	.request-hover.has-no-actions,
	.request-hover.hidden {
		display: none !important;
	}
	.request-hover {
		position: absolute;
		overflow: hidden;
		z-index: 100;
		background-color: var(--vscode-interactive-result-editor-background-color, var(--vscode-editor-background));
		border: 1px solid var(--vscode-chat-requestBorder);
		top: -13px;
		right: 20px;
		border-radius: 3px;
		width: 28px;
		height: 26px;
	}

	.request-hover.expanded {
		width: 50px;
	}

	.request-hover.editing {
		opacity: 1 !important;
	}

	.request-hover:not(.expanded) .actions-container {
		width: 22px;
		height: 22px;
	}

	.request-hover.expanded .actions-container {
		padding: 0 3px;
	}

	.request-hover:not(.expanded) .actions-container{
		.action-label.codicon-discard,
		.action-label.codicon-x,
		.action-label.codicon-edit {
			margin-top: 4px;
			padding: 3px 3px;
		}
	}

	.request-hover:focus-within {
		opacity: 1 !important;
	}

	.interactive-list > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row {
		overflow: visible !important;
	}

	.interactive-list > .monaco-list > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row.monaco-list-row.focused.request {
		outline: none !important;
	}

	div[data-index="0"] .monaco-tl-contents {
		.interactive-item-container.interactive-request:not(.editing) {
			padding-top: 19px;
		}

		.request-hover {
			top: 0px;
		}
	}

	.interactive-list > .monaco-list:focus > .monaco-scrollable-element > .monaco-list-rows > .monaco-list-row.focused.request {
		outline: none !important;

		.interactive-item-container .value .rendered-markdown {
			outline: 1px solid var(--vscode-focusBorder);
		}

		.request-hover {
			display: block !important;
		}
	}

	.interactive-request.editing .rendered-markdown,
	.interactive-request.editing .value {
		display: none;
	}

	.interactive-request.editing-input .rendered-markdown {
		outline: 1px solid var(--vscode-focusBorder);
	}

	.interactive-request.editing {
		padding: 0px;
	}
}


.chat-buttons-container {
	display: flex;
	gap: 8px;
	margin-top: 0px;
	flex-wrap: wrap;
	flex-basis: 100%;
	padding: 0 8px;
	margin: 8px 0;

	& .monaco-button.monaco-dropdown-button {
		padding: 0 3px;
	}
}

.interactive-item-container .chat-command-button .monaco-button,
.chat-buttons-container .monaco-button:not(.monaco-dropdown-button) {
	text-align: left;
	width: initial;
	padding: 4px 8px;
}

.interactive-item-container .chat-edit-input-container {
	width: 100%;
}

.chat-row-disabled-overlay,
.interactive-item-container .chat-edit-input-container .chat-editing-session {
	display: none;
}

.chat-row-disabled-overlay.disabled,
.chat-input-overlay.disabled {
	position: absolute;
	width: 100%;
	height: 100%;
	background-color: var(--vscode-sideBar-background);
	opacity: 0.6;
	pointer-events: none;
	display: flex;
	cursor: default;
	z-index: 101;
	user-select: none;
}

.interactive-session .focused-input-dom {
	position:absolute;
	top: -50000px;
	width: 1px;
	height: 1px;
}
