/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.pane-body.chat-view-welcome-visible {
	& > .interactive-session {
		display: none;
	}

	& > .chat-view-welcome {
		display: flex;
	}
}

/* Container for chat widget welcome message */
.interactive-session .chat-welcome-view-container {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
}

.interactive-session .experimental-welcome-view & > .chat-welcome-view-input-part {
	max-width: 650px;
	margin-bottom: 48px;
}

.interactive-session.experimental-welcome-view .chat-input-toolbars > .chat-input-toolbar > div {
	display: none;
}

/* Container for ChatViewPane welcome view */
.pane-body > .chat-view-welcome {
	flex-direction: column;
	justify-content: center;
	overflow: hidden;
	height: 100%;
	display: none;
}

div.chat-welcome-view {
	display: flex;
	flex-direction: column;
	align-items: center;

	& > .chat-welcome-view-icon {
		min-height: 48px;
	}

	& > .chat-welcome-view-icon .codicon {
		font-size: 40px;
	}

	& > .chat-welcome-view-title {
		font-size: 24px;
		margin-top: 5px;
		font-weight: 500;
		text-align: center;
		line-height: normal;
		padding: 0 8px;
	}

	& > .chat-welcome-view-indicator-container {
		display: flex;
		flex-wrap: wrap;
		margin-top: 5px;
		gap: 9px;
		justify-content: center;
	}

	& > .chat-welcome-view-message {
		text-align: center;
		max-width: 350px;
		padding: 0 20px;
		margin-top: 10px;

		a {
			color: var(--vscode-textLink-foreground);
		}
	}

	.monaco-button {
		display: inline-block;
		width: initial;
		padding: 4px 7px;
	}

	& > .chat-welcome-view-tips {
		max-width: 250px;
		margin: 10px 5px 0px;

		.rendered-markdown {
			gap: 6px;
			display: flex;
			align-items: start;
			flex-direction: column;
		}

		.rendered-markdown p {
			display: flex;
			gap: 6px;
			margin: 6px 0 0 0;

			.codicon {
				padding-top: 1px;
			}
		}
	}

	& > .chat-welcome-experimental-view-message {
		text-align: center;
		max-width: 350px;
		padding: 0 20px 32px;
		font-size: 16px;

		a {
			color: var(--vscode-descriptionForeground);
		}
	}

	& > .chat-welcome-view-experimental-additional-message {
		font-size: 12px;
		color: var(--vscode-disabledForeground);
		text-align: center;
		max-width: 400px;
		margin-top: 8px;
	}

	& > .chat-welcome-view-suggested-prompts {
		display: flex;
		flex-wrap: wrap;
		justify-content: center;
		margin-top: 4px;

		> .chat-welcome-view-suggested-prompt {
			display: flex;
			align-items: center;
			padding: 2px;
			border-radius: 8px;
			background-color: var(--vscode-editorWidget-background);
			cursor: pointer;
			border: 1px solid var(--vscode-chat-requestBorder, var(--vscode-input-background, transparent));
			border-radius: 4px;
			max-width: 100%;
			width: fit-content;
			margin: 0 4px;

			> .chat-welcome-view-suggested-prompt-icon {
				display: flex;
				align-items: center;
				font-size: 4px;
				color: var(--vscode-icon-foreground) !important;
				align-items: center;
				padding: 4px;
			}

			> .chat-welcome-view-suggested-prompt-label {
				font-size: 14px;
				color: var(--vscode-editorWidget-foreground);
				padding: 4px 4px 4px 0;
			}
		}

		> .chat-welcome-view-suggested-prompt:hover {
			background-color: var(--vscode-list-hoverBackground);
			border-color: var(--vscode-focusBorder);
		}
	}
}
