on:
  workflow_call:
    inputs:
      job_name:
        type: string
        required: true
      electron_tests:
        type: boolean
        default: false
      browser_tests:
        type: boolean
        default: false
      remote_tests:
        type: boolean
        default: false

jobs:
  macOS-test:
    name: ${{ inputs.job_name }}
    runs-on: macos-14-xlarge
    env:
      ARTIFACT_NAME: ${{ (inputs.electron_tests && 'electron') || (inputs.browser_tests && 'browser') || (inputs.remote_tests && 'remote') || 'unknown' }}
      NPM_ARCH: arm64
      VSCODE_ARCH: arm64
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
        env:
          NODEJS_ORG_MIRROR: https://github.com/joaomoreno/node-mirror/releases/download

      - name: Prepare node_modules cache key
        run: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js darwin $VSCODE_ARCH $(node -p process.arch) > .build/packagelockhash

      - name: Restore node_modules cache
        id: cache-node-modules
        uses: actions/cache/restore@v4
        with:
          path: .build/node_modules_cache
          key: "node_modules-${{ hashFiles('.build/packagelockhash') }}"

      - name: Extract node_modules cache
        if: steps.cache-node-modules.outputs.cache-hit == 'true'
        run: tar -xzf .build/node_modules_cache/cache.tgz

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          c++ --version
          xcode-select -print-path
          python3 -m pip install --break-system-packages setuptools

          for i in {1..5}; do # try 5 times
            npm ci && break
            if [ $i -eq 5 ]; then
              echo "Npm install failed too many times" >&2
              exit 1
            fi
            echo "Npm install failed $i, trying again..."
          done
        env:
          npm_config_arch: ${{ env.NPM_ARCH }}
          VSCODE_ARCH: ${{ env.VSCODE_ARCH }}
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          # Avoid using dlopen to load Kerberos on macOS which can cause missing libraries
          # https://github.com/mongodb-js/kerberos/commit/04044d2814ad1d01e77f1ce87f26b03d86692cf2
          # flipped the default to support legacy linux distros which shouldn't happen
          # on macOS.
          GYP_DEFINES: "kerberos_use_rtld=false"

      - name: Create node_modules archive
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
          mkdir -p .build/node_modules_cache
          tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt

      - name: Create .build folder
        run: mkdir -p .build

      - name: Prepare built-in extensions cache key
        run: node build/azure-pipelines/common/computeBuiltInDepsCacheKey.js > .build/builtindepshash

      - name: Restore built-in extensions cache
        id: cache-builtin-extensions
        uses: actions/cache/restore@v4
        with:
          path: .build/builtInExtensions
          key: "builtin-extensions-${{ hashFiles('.build/builtindepshash') }}"

      - name: Download built-in extensions
        if: steps.cache-builtin-extensions.outputs.cache-hit != 'true'
        run: node build/lib/builtInExtensions.js
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Transpile client and extensions
        run: npm run gulp transpile-client-esbuild transpile-extensions

      - name: Download Electron and Playwright
        run: |
          set -e

          for i in {1..3}; do # try 3 times (matching retryCountOnTaskFailure: 3)
            if npm exec -- npm-run-all -lp "electron ${{ env.VSCODE_ARCH }}" "playwright-install"; then
              echo "Download successful on attempt $i"
              break
            fi

            if [ $i -eq 3 ]; then
              echo "Download failed after 3 attempts" >&2
              exit 1
            fi

            echo "Download failed on attempt $i, retrying..."
            sleep 5 # optional: add a small delay between retries
          done
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: 🧪 Run unit tests (Electron)
        if: ${{ inputs.electron_tests }}
        timeout-minutes: 15
        run: ./scripts/test.sh --tfs "Unit Tests"

      - name: 🧪 Run unit tests (node.js)
        if: ${{ inputs.electron_tests }}
        timeout-minutes: 15
        run: npm run test-node

      - name: 🧪 Run unit tests (Browser, Webkit)
        if: ${{ inputs.browser_tests }}
        timeout-minutes: 30
        run: npm run test-browser-no-install -- --browser webkit --tfs "Browser Unit Tests"
        env:
          DEBUG: "*browser*"

      - name: Build integration tests
        run: |
          set -e
          npm run gulp \
            compile-extension:configuration-editing \
            compile-extension:css-language-features-server \
            compile-extension:emmet \
            compile-extension:git \
            compile-extension:github-authentication \
            compile-extension:html-language-features-server \
            compile-extension:ipynb \
            compile-extension:notebook-renderers \
            compile-extension:json-language-features-server \
            compile-extension:markdown-language-features \
            compile-extension-media \
            compile-extension:microsoft-authentication \
            compile-extension:typescript-language-features \
            compile-extension:vscode-api-tests \
            compile-extension:vscode-colorize-tests \
            compile-extension:vscode-colorize-perf-tests \
            compile-extension:vscode-test-resolver

      - name: 🧪 Run integration tests (Electron)
        if: ${{ inputs.electron_tests }}
        timeout-minutes: 20
        run: ./scripts/test-integration.sh --tfs "Integration Tests"

      - name: 🧪 Run integration tests (Browser, Webkit)
        if: ${{ inputs.browser_tests }}
        timeout-minutes: 20
        run: ./scripts/test-web-integration.sh --browser webkit

      - name: 🧪 Run integration tests (Remote)
        if: ${{ inputs.remote_tests }}
        timeout-minutes: 20
        run: ./scripts/test-remote-integration.sh

      - name: Compile smoke tests
        working-directory: test/smoke
        run: npm run compile

      - name: Compile extensions for smoke tests
        run: npm run gulp compile-extension-media

      - name: Diagnostics before smoke test run
        run: ps -ef
        continue-on-error: true
        if: always()

      - name: 🧪 Run smoke tests (Electron)
        if: ${{ inputs.electron_tests }}
        timeout-minutes: 20
        run: npm run smoketest-no-compile -- --tracing

      - name: 🧪 Run smoke tests (Browser, Chromium)
        if: ${{ inputs.browser_tests }}
        timeout-minutes: 20
        run: npm run smoketest-no-compile -- --web --tracing --headless

      - name: 🧪 Run smoke tests (Remote)
        if: ${{ inputs.remote_tests }}
        timeout-minutes: 20
        run: npm run smoketest-no-compile -- --remote --tracing

      - name: Diagnostics after smoke test run
        run: ps -ef
        continue-on-error: true
        if: always()

      - name: Publish Crash Reports
        uses: actions/upload-artifact@v4
        if: failure()
        continue-on-error: true
        with:
          name: ${{ format('crash-dump-macos-{0}-{1}-{2}', env.VSCODE_ARCH, env.ARTIFACT_NAME, github.run_attempt) }}
          path: .build/crashes
          if-no-files-found: ignore

      # In order to properly symbolify above crash reports
      # (if any), we need the compiled native modules too
      - name: Publish Node Modules
        uses: actions/upload-artifact@v4
        if: failure()
        continue-on-error: true
        with:
          name: ${{ format('node-modules-macos-{0}-{1}-{2}', env.VSCODE_ARCH, env.ARTIFACT_NAME, github.run_attempt) }}
          path: node_modules
          if-no-files-found: ignore

      - name: Publish Log Files
        uses: actions/upload-artifact@v4
        if: always()
        continue-on-error: true
        with:
          name: ${{ format('logs-macos-{0}-{1}-{2}', env.VSCODE_ARCH, env.ARTIFACT_NAME, github.run_attempt) }}
          path: .build/logs
          if-no-files-found: ignore
